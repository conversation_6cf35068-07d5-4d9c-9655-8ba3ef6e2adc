# Rate Limits

> Rate limits for different pricing plans and API requests

## Concurrent Browser Limits

Concurrent browsers represent how many web pages Firecrawl can process for you at the same time.
Your plan determines how many of these jobs can run simultaneously - if you exceed this limit,
additional jobs will wait in a queue until resources become available.

### Standard Plans (most API endpoints)

| Plan     | Concurrent Browsers |
| -------- | ------------------- |
| Free     | 2                   |
| Hobby    | 5                   |
| Standard | 50                  |
| Growth   | 100                 |

### Extract plans (/extract API)

| Plan     | Concurrent Browsers |
| -------- | ------------------- |
| Free     | 2                   |
| Starter  | 50                  |
| Explorer | 100                 |
| Pro      | 200                 |

## Standard API

The following rate limits apply to standard API requests and are primarily in place to prevent abuse:

| Plan     | /scrape (requests/min) | /map (requests/min) | /crawl (requests/min) | /search (requests/min) |
| -------- | ---------------------- | ------------------- | --------------------- | ---------------------- |
| Free     | 10                     | 10                  | 1                     | 5                      |
| Hobby    | 100                    | 100                 | 15                    | 50                     |
| Standard | 500                    | 500                 | 50                    | 250                    |
| Growth   | 5000                   | 5000                | 250                   | 2500                   |

|         | /crawl/status (requests/min) |
| ------- | ---------------------------- |
| Default | 1500                         |

These rate limits are enforced to ensure fair usage and availability of the API for all users. If you require higher limits, please contact us at [<EMAIL>](mailto:<EMAIL>) to discuss custom plans.

### Batch Endpoints

Batch endpoints follow the /crawl rate limit.

## Extract API

| Plan       | /extract (requests/min) |
| ---------- | ----------------------- |
| Free       | 10                      |
| Starter    | 100                     |
| Explorer   | 500                     |
| Pro        | 1000                    |
| Enterprise | Custom                  |

|      | /extract/status (requests/min) |
| ---- | ------------------------------ |
| Free | 500                            |

## FIRE-1 Agent

Requests involving the FIRE-1 agent requests have separate rate limits that are counted independently for each endpoint:

| Endpoint   | Rate Limit (requests/min) |
| ---------- | ------------------------- |
| `/scrape`  | 10                        |
| `/extract` | 10                        |

## Legacy Plans

| Plan            | /scrape (requests/min) | /crawl (concurrent req) | /search (requests/min) |
| --------------- | ---------------------- | ----------------------- | ---------------------- |
| Starter         | 100                    | 15                      | 100                    |
| Standard Legacy | 200                    | 200                     | 200                    |
| Scaled Legacy   | 250                    | 100                     | 250                    |

If you require higher limits, please contact us at [<EMAIL>](mailto:<EMAIL>) to discuss custom plans.
