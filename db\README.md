# Database Setup with Supabase

This project uses [Supabase](https://supabase.com/) as the PostgreSQL database provider with [Drizzle ORM](https://orm.drizzle.team/) for database operations.

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```env
# Supabase PostgreSQL
# Use the connection pooler URL for serverless environments
# Format: postgresql://postgres.[project-ref]:[password]@aws-0-[region].pooler.supabase.com:6543/postgres
DATABASE_URL=your_supabase_connection_string

# Optional: Supabase Configuration (for additional Supabase features)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### Getting Your Supabase Connection String

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to Settings → Database
4. Copy the connection string from the "Connection Pooling" section
5. Replace `[YOUR-PASSWORD]` with your actual database password

**Important**: Use the Connection Pooler URL for serverless environments (like Vercel), and the Direct Connection for long-running servers.

## Database Connection

The database connection is configured in `db/drizzle.ts`:

- Uses `postgres-js` driver for optimal performance with Supabase
- Disables prepared statements (`prepare: false`) for compatibility with Supabase's Transaction pool mode
- Supports connection pooling for serverless environments

## Schema

The database schema is defined in `db/schema.ts` and includes:

- User authentication tables (Better Auth)
- Subscription management tables (Polar integration)
- All tables use PostgreSQL-specific types from `drizzle-orm/pg-core`

## Migrations

Run database migrations using Drizzle Kit:

```bash
# Generate migrations
bun run drizzle-kit generate

# Apply migrations
bun run drizzle-kit migrate
```

## Development

For local development, you can use Supabase's local development setup or connect directly to your hosted Supabase instance.
